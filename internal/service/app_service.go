package service

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"net/url"
	"strings"
	"time"

	"github.com/inv-cloud-platform/fe-ev-com-ocpi-go/ocpi"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/common/config"
	"github.com/inv-cloud-platform/fe-ev-csms-io/internal/domain"
	"github.com/inv-cloud-platform/hub-com-auth-go/hubauth"
	"github.com/inv-cloud-platform/hub-com-metrics-go/hubmetrics"
	"github.com/rs/zerolog/log"
	"github.com/samber/lo/mutable"
	"resty.dev/v3"
)

const (
	telemetryEvent  = "telemetry"
	SourceLabel     = "source"
	SinkLabel       = "sink"
	CommandLabel    = "command"
	UccCommandLabel = "ucc_command"
	TimeOut         = 10
)

type AppService struct {
	cfg    *config.Config
	client *resty.Client
	conSrv *ConnectionService
	token  hubauth.Token
}

func (a *AppService) ProcessEvent(ctx context.Context, message []byte, subject string) error {
	deviceID, errSubDetails := a.getDeviceID(subject)
	if errSubDetails != nil {
		log.Err(errSubDetails).Msg("error to get subject information")
		return nil
	}
	evses, err := a.getEvses(ctx, deviceID)
	if err != nil {
		log.Err(err).Msg("error to get evses")
		return nil
	}

	for _, evse := range evses {
		shouldBreak := false
		errMsg := a.handleMessage(ctx, message, subject, evse, func(module string) {
			if module == domain.Tariffs || module == domain.Locations {
				shouldBreak = true
			}
		})
		if errMsg != nil {
			log.Err(errMsg).Msg("error to handle message")
		}
		if shouldBreak {
			break
		}
	}
	return nil
}

func (*AppService) isSyncModuleEvent(subject string) bool {
	sub := strings.ToLower(subject)
	return strings.Contains(sub, strings.ToLower(domain.SyncOcpiObjectEvent))
}

func (*AppService) isOcpiCommandEvent(subject string) bool {
	sub := strings.ToLower(subject)
	return strings.Contains(sub, strings.ToLower(domain.OcpiCommandEvent))
}

func (a *AppService) handleMessage(ctx context.Context, message []byte, subject string, evse domain.EvseData, callback func(module string)) error {
	connInfo := evse.Connection
	connection, errConnDetails := a.conSrv.GetConnectionDetails(ctx, connInfo.CountryCode, connInfo.PartyID)
	if errConnDetails != nil {
		return errConnDetails
	}
	if a.isSyncModuleEvent(subject) {
		errSyncModule := a.processSyncModule(ctx, message, connection, evse, callback)
		if errSyncModule != nil {
			return errSyncModule
		}
	} else if a.isOcpiCommandEvent(subject) {
		errOcpiCommand := a.processOcpiCommand(ctx, message, connection, evse)
		if errOcpiCommand != nil {
			return errOcpiCommand
		}
	}
	return nil
}

func (*AppService) getDeviceID(subject string) (string, error) {
	parts := strings.Split(subject, ".")
	if len(parts) > 1 {
		deviceID := parts[len(parts)-1]
		return deviceID, nil
	}
	return "", domain.ErrSubjectIsNotValid
}

func (*AppService) getCommandEndpoint(baseCommandURL string, cmdType domain.CommandType) string {
	switch cmdType {
	case domain.CommandTypeCancelReservation:
		return common.BuildCommandEndpoint(baseCommandURL, common.CommandCancelReservation)
	case domain.CommandTypeReserveNow:
		return common.BuildCommandEndpoint(baseCommandURL, common.CommandReserveNow)
	case domain.CommandTypeStartSession:
		return common.BuildCommandEndpoint(baseCommandURL, common.CommandStartSession)
	case domain.CommandTypeStopSession:
		return common.BuildCommandEndpoint(baseCommandURL, common.CommandStopSession)
	case domain.CommandTypeUnlockConnector:
		return common.BuildCommandEndpoint(baseCommandURL, common.CommandUnlockConnector)
	default:
		return common.BuildCommandEndpoint(baseCommandURL, string(cmdType))
	}
}

func (*AppService) getRequestBody(cmd domain.Command, connDetails *domain.EMSPInformation) ([]byte, error) {
	responseURL := fmt.Sprintf("%s/%s/commands/%s/%s", connDetails.URL, connDetails.Version, cmd.Payload.CommandType, cmd.Payload.TrackingID)
	var data map[string]any
	if err := json.Unmarshal(cmd.Payload.RequestBody, &data); err != nil {
		return nil, err
	}
	data["response_url"] = responseURL
	return json.Marshal(data)
}

func (a *AppService) sendToCPO(ctx context.Context, cmd domain.Command, connection *domain.Connection) error {
	cpoInfo := connection.CPOInformation()
	emspInfo := connection.EMSPInformation()
	response := &domain.CommandResponse{}
	endpoint := a.getCommandEndpoint(cpoInfo.CommandsURL, cmd.Payload.CommandType)
	requestBody, err := a.getRequestBody(cmd, emspInfo)
	if err != nil {
		return err
	}
	resp, err := a.restyRequest(ctx, connection).
		SetResult(response).
		SetBody(requestBody).
		Post(endpoint)
	if err != nil {
		return err
	}
	if resp.IsError() {
		return fmt.Errorf("error to post command url:%v ", endpoint)
	}
	log.Info().Str("URL", endpoint).Int("Status code", response.StatusCode).Msg("post command success")
	elvisCode := domain.StatusSuccess
	if response.StatusCode >= domain.StatusCodeInvalid {
		elvisCode = domain.StatusCallbackTimeout
	}
	uccCommandRequest := domain.UccCommand{
		CommandName: domain.UccCommandName,
		Params: domain.UccParams{
			StatusCode:      response.StatusCode,
			ElvisCode:       elvisCode,
			TrackingID:      cmd.Payload.TrackingID,
			CommandResponse: response.Data,
		},
		Targets: []string{cmd.DeviceID},
	}
	errUcc := a.postToUCC(ctx, uccCommandRequest)
	if errUcc != nil {
		return errUcc
	}
	return nil
}

func (a *AppService) postToUCC(ctx context.Context, body any) error {
	uccOpsURL := a.cfg.HTTP.UCCHost + "/operations"
	log.Info().Str("url", uccOpsURL).Any("body", body).Msg("before post to UCC")
	var res any
	resp, err := a.client.R().
		SetContext(ctx).
		SetAuthToken(a.token.AccessToken()).
		SetRetryWaitTime(a.cfg.HTTP.Retry.InitialInterval).
		SetRetryCount(a.cfg.HTTP.Retry.MaxAttempts).
		SetForceResponseContentType(common.ContentTypeJSON).
		SetBody(body).
		SetResult(&res).
		Post(uccOpsURL)
	if err != nil {
		return err
	}
	if resp.IsError() {
		return errors.New("unable to send command to ucc: [" + resp.Status() + "] - " + resp.String())
	}
	log.Info().Any("UCC Result", res).Int("Status Code", resp.StatusCode()).Msg("UCC Post response")
	return nil
}

func (a *AppService) getEvses(ctx context.Context, deviceID string) ([]domain.EvseData, error) {
	// /device/evses/{device_id}
	res := &domain.EvseResponse{}
	evseURL := a.cfg.HTTP.EvseHost + "/device/evses/" + deviceID
	resp, err := a.client.R().
		SetContext(ctx).
		SetRetryWaitTime(a.cfg.HTTP.Retry.InitialInterval).
		SetRetryCount(a.cfg.HTTP.Retry.MaxAttempts).
		SetForceResponseContentType(common.ContentTypeJSON).
		SetResult(res).
		Get(evseURL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, errors.New("error to evses: url" + evseURL + " [" + resp.Status() + "] - " + resp.String())
	}
	log.Info().Str("url", evseURL).Int("status code", resp.StatusCode()).Msg("response from /device/evses/" + deviceID)
	return res.Data, nil
}

// us-dom-START_SESSION-US-GVR-8-63-00001
func (*AppService) parseEvseUIDFromTrackingID(trackingID string) (*domain.TrackingInformation, error) {
	trackingPartLength := 7
	parts := strings.Split(trackingID, "-")
	if len(parts) < trackingPartLength {
		return nil, errors.New("invalid tracking id")
	}
	totalLength := len(parts)
	return &domain.TrackingInformation{
		CountryCode: parts[0],
		PartyID:     parts[1],
		CommandName: parts[2],
		LocationID:  parts[totalLength-3],
		EvseUID:     parts[totalLength-2],
	}, nil
}

func (a *AppService) processOcpiCommand(ctx context.Context, message []byte, connection *domain.Connection, evse domain.EvseData) error {
	startTime := time.Now()
	event := &domain.TelemetryEvent{}
	if err := json.Unmarshal(message, event); err != nil {
		log.Err(err).Msg("unmarshal error")
		return err
	}
	for _, cmd := range event.Data {
		trackingInfo, errParse := a.parseEvseUIDFromTrackingID(cmd.Payload.TrackingID)
		if errParse != nil {
			log.Err(errParse).Str("tracking_id", cmd.Payload.TrackingID).Str("command_type", string(cmd.Payload.CommandType)).Msg("failed to parse evse")
			continue
		}
		if trackingInfo.EvseUID != evse.EvseUID {
			continue
		}
		hubmetrics.Ingestor.EventsTotalRead(map[string]string{"operation": telemetryEvent}, 1)
		if err := a.sendToCPO(ctx, cmd, connection); err != nil {
			log.Err(err).Str("tracking_id", cmd.Payload.TrackingID).Str("command_type", string(cmd.Payload.CommandType)).Msg("failed to process command")
			return fmt.Errorf("failed to process command: %w", err)
		}
		hubmetrics.Ingestor.EventsProcessedTotal(map[string]string{"operation": telemetryEvent}, 1)
		log.Info().Int64("duration_milliseconds", time.Since(startTime).Milliseconds()).
			Str("tracking_id", cmd.Payload.TrackingID).
			Str("command_type", string(cmd.Payload.CommandType)).Msg("command processed successfully")
	}
	return nil
}

func (a *AppService) locationSync(ctx context.Context, evse domain.EvseData, connection *domain.Connection) (*domain.UccSyncRequest, error) {
	response := &ocpi.LocationResponse{}
	locationURL, errURL := connection.LocationURL(evse)
	if errURL != nil {
		return nil, errURL
	}
	resp, err := a.restyRequest(ctx, connection).SetResult(response).Get(locationURL)
	if err != nil {
		return nil, err
	}
	if resp.IsError() {
		return nil, errors.New("unable to get location: [" + resp.Status() + "] url: " + locationURL + " resp:" + resp.String())
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.New("response not ok for locations: [" + resp.Status() + "] url: " + locationURL + " resp:" + resp.String())
	}
	value, errMarshal := json.Marshal(response.Data)
	if errMarshal != nil {
		return nil, errMarshal
	}
	version := connection.EMSPInformation().Version
	objectPathURL := a.cfg.HTTP.LocationHost + "/ocpi/" + strings.ToLower(evse.Connection.CountryCode) + "/" + strings.ToLower(evse.Connection.PartyID) +
		"/emsp/" + version + "/locations/" + evse.ChargerID
	uccReq := &domain.UccSyncRequest{
		CommandName: domain.UccObjectUpdateCommand,
		Params: domain.SyncUccParams{
			Method:     http.MethodPut,
			ObjectPath: objectPathURL,
			Module:     domain.Locations,
			Value:      string(value),
		},
		Targets: []string{},
	}
	return uccReq, nil
}

func (a *AppService) urlByModule(module string, connection *domain.Connection) (string, error) {
	endpoint, errURL := connection.GetURL(module)
	if errURL != nil {
		return "", errURL
	}
	endpoint, err := a.fromAndToDate(endpoint)
	if err != nil {
		return "", err
	}
	return endpoint, nil
}

func (a *AppService) fromAndToDate(baseURL string) (string, error) {
	now := time.Now().UTC()
	from := now.AddDate(0, 0, -a.cfg.Service.NoOfDays)

	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}
	q := u.Query()

	if strings.Contains(baseURL, TariffKey) {
		u.RawQuery = q.Encode()
		return u.String(), nil
	}
	q.Set("date_from", from.Format(time.RFC3339))
	q.Set("date_to", now.Format(time.RFC3339))
	u.RawQuery = q.Encode()
	return u.String(), nil
}

func (a *AppService) cdrSync(ctx context.Context, evse domain.EvseData, connection *domain.Connection) (*domain.UccSyncRequest, error) {
	response := ocpi.CdrsResponse{}
	endpoint, errURL := a.urlByModule(domain.Cdrs, connection)
	if errURL != nil {
		return nil, errURL
	}
	start := time.Now()
	resp, err := a.restyRequest(ctx, connection).
		SetResult(&response).
		Get(endpoint)
	duration := time.Since(start).Seconds()

	module := "cdrs"
	status := "success"
	if err != nil || resp.IsError() {
		status = "error"
		errorType := "http_error"
		if err != nil {
			errorType = "request_error"
		}
		common.RestyRequestErrors.WithLabelValues(module, errorType).Inc()

	}

	common.RestyRequestDuration.WithLabelValues(module, status).Observe(duration)
	if resp.IsError() {
		return nil, errors.New("unable to get cdrs: [" + resp.Status() + "] url: " + endpoint + " resp:" + resp.String())
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.New("response is not ok for cdrs: [" + resp.Status() + "] url: " + endpoint + " resp:" + resp.String())
	}
	var cdrFinal ocpi.CDR
	mutable.Reverse(response.Data) // Reverse it because latest data is in last
	for idx := range response.Data {
		cdr := response.Data[idx]
		if cdr.CdrLocation.EvseUID != nil && *cdr.CdrLocation.EvseUID == evse.EvseUID {
			cdrFinal = cdr
			break
		}
	}
	if cdrFinal.ID == "" {
		return nil, errors.New("no cdr found")
	}
	value, errMarshal := json.Marshal(cdrFinal)
	if errMarshal != nil {
		return nil, errMarshal
	}
	version := connection.EMSPInformation().Version
	objectPathURL := a.cfg.HTTP.CDRHost + "/ocpi/" + strings.ToLower(evse.Connection.CountryCode) + "/" + strings.ToLower(evse.Connection.PartyID) +
		"/emsp/" + version + "/cdrs"
	uccReq := &domain.UccSyncRequest{
		CommandName: domain.UccObjectUpdateCommand,
		Params: domain.SyncUccParams{
			Method:     http.MethodPut,
			ObjectPath: objectPathURL,
			Module:     domain.Cdrs,
			Value:      string(value),
		},
		Targets: []string{},
	}
	return uccReq, nil
}

func (a *AppService) sessionSync(ctx context.Context, evse domain.EvseData, connection *domain.Connection) (*domain.UccSyncRequest, error) {
	response := ocpi.SessionsResponse{}
	endpoint, errURL := a.urlByModule(domain.Sessions, connection)
	if errURL != nil {
		return nil, errURL
	}

	start := time.Now()
	resp, err := a.restyRequest(ctx, connection).
		SetResult(&response).
		Get(endpoint)
	duration := time.Since(start).Seconds()

	module := "sessions"
	status := "success"
	if err != nil || resp.IsError() {
		status = "error"
		errorType := "http_error"
		if err != nil {
			errorType = "request_error"
		}
		common.RestyRequestErrors.WithLabelValues(module, errorType).Inc()

	}

	common.RestyRequestDuration.WithLabelValues(module, status).Observe(duration)

	if resp.IsError() {
		return nil, errors.New("unable to get sessions: [" + resp.Status() + "] url: " + endpoint + " resp:" + resp.String())
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.New("response is not ok for sessions: [" + resp.Status() + "] url: " + endpoint + " resp:" + resp.String())
	}
	mutable.Reverse(response.Data) // Reverse it because latest data is in last
	var sessionData ocpi.Session
	for idx := range response.Data {
		session := response.Data[idx]
		if session.EvseUID == evse.EvseUID {
			sessionData = session
			break
		}
	}
	if sessionData.ID == "" {
		return nil, errors.New("no session found")
	}
	value, errMarshal := json.Marshal(sessionData)
	if errMarshal != nil {
		return nil, errMarshal
	}
	version := connection.EMSPInformation().Version
	objectPathURL := a.cfg.HTTP.CDRHost + "/ocpi/" + strings.ToLower(evse.Connection.CountryCode) + "/" + strings.ToLower(evse.Connection.PartyID) +
		"/emsp/" + version + "/sessions"
	uccReq := &domain.UccSyncRequest{
		CommandName: domain.UccObjectUpdateCommand,
		Params: domain.SyncUccParams{
			Method:     http.MethodPut,
			ObjectPath: objectPathURL,
			Module:     domain.Sessions,
			Value:      string(value),
		},
		Targets: []string{},
	}
	return uccReq, nil
}

func (a *AppService) restyRequest(ctx context.Context, connection *domain.Connection) *resty.Request {
	cpoInfo := connection.CPOInformation()
	emspInfo := connection.EMSPInformation()
	cpoToken := base64.StdEncoding.EncodeToString([]byte(cpoInfo.Token))
	request := a.client.R().
		SetContext(ctx).
		SetContentType(common.ContentTypeJSON).
		SetHeader(common.HeaderAuthorization, "Token "+cpoToken).
		SetHeader(common.HeaderOCPIFromPartyID, emspInfo.PartyID).
		SetHeader(common.HeaderOCPIFromCountryCode, emspInfo.CountryCode).
		SetHeader(common.HeaderOCPIToPartyID, cpoInfo.PartyID).
		SetHeader(common.HeaderOCPIToCountryCode, cpoInfo.CountryCode).
		SetRetryWaitTime(a.cfg.HTTP.Retry.InitialInterval).
		SetRetryCount(a.cfg.HTTP.Retry.MaxAttempts).
		SetForceResponseContentType(common.ContentTypeJSON)
	return request
}

func (a *AppService) getTariffs(ctx context.Context, connection *domain.Connection) (*ocpi.TariffsResponse, error) {
	response := &ocpi.TariffsResponse{}
	tariffsURL, errURL := a.urlByModule(domain.Tariffs, connection)
	if errURL != nil {
		return nil, errURL
	}

	start := time.Now()
	resp, err := a.restyRequest(ctx, connection).SetResult(response).Get(tariffsURL)
	duration := time.Since(start).Seconds()

	status := "success"
	if err != nil || resp.IsError() {
		status = "error"
		errorType := "http_error"
		if err != nil {
			errorType = "request_error"
		}
		common.RestyRequestErrors.WithLabelValues(tariffsURL, errorType).Inc()
	}

	common.RestyRequestDuration.WithLabelValues(tariffsURL, status).Observe(duration)

	if resp.IsError() {
		return nil, errors.New("unable to get tariffs: [" + resp.Status() + "] url: " + tariffsURL + " resp:" + resp.String())
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.New("response is not ok for tariffs: [" + resp.Status() + "] url: " + tariffsURL + " resp:" + resp.String())
	}
	return response, nil
}

func (a *AppService) getLocation(ctx context.Context, evse domain.EvseData, connection *domain.Connection) (*ocpi.LocationResponse, error) {
	response := &ocpi.LocationResponse{}
	locationURL, errURL := connection.LocationURL(evse)
	if errURL != nil {
		return nil, errURL
	}

	start := time.Now()
	resp, err := a.restyRequest(ctx, connection).SetResult(response).Get(locationURL)
	duration := time.Since(start).Seconds()

	status := "success"
	if err != nil || resp.IsError() {
		status = "error"
		errorType := "http_error"
		if err != nil {
			errorType = "request_error"
		}
		common.RestyRequestErrors.WithLabelValues(locationURL, errorType).Inc()
	}

	common.RestyRequestDuration.WithLabelValues(locationURL, status).Observe(duration)
	if resp.IsError() {
		return nil, errors.New("unable to get location: [" + resp.Status() + "] url: " + locationURL + " resp:" + resp.String())
	}
	if resp.StatusCode() != http.StatusOK {
		return nil, errors.New("response not ok for locations: [" + resp.Status() + "] url: " + locationURL + " resp:" + resp.String())
	}
	return response, nil
}

func (a *AppService) tariffSync(ctx context.Context, evse domain.EvseData, connection *domain.Connection) (*domain.UccSyncRequest, error) {
	tariffResponse, err := a.getTariffs(ctx, connection)
	if err != nil {
		log.Err(err).Msg("get tariffs error")
		return nil, err
	}
	// Get location
	locationResponse, errLoc := a.getLocation(ctx, evse, connection)
	if errLoc != nil {
		log.Err(errLoc).Msg("get location error")
		return nil, err
	}
	tariffIDs := []string{}
	for _, evse := range locationResponse.Data.Evses {
		for _, conector := range evse.Connectors {
			tariffIDs = append(tariffIDs, conector.TariffIDS...)
		}
	}
	if len(tariffIDs) == 0 {
		return nil, fmt.Errorf("there is no tariff associated with location %v", evse.ChargerID)
	}

	tariffID := tariffIDs[0]
	tariffData := &ocpi.Tariff{}
	for _, item := range tariffResponse.Data {
		if item.ID != tariffID {
			continue
		}
		tariffData = &item
	}
	if tariffData.ID == "" {
		return nil, fmt.Errorf("no tariff id=%v found in tariffs", tariffID)
	}
	value, errMarshal := json.Marshal(tariffData)
	if errMarshal != nil {
		return nil, errMarshal
	}
	version := connection.EMSPInformation().Version
	objectPathURL := a.cfg.HTTP.CDRHost + "/ocpi/" + strings.ToLower(evse.Connection.CountryCode) + "/" + strings.ToLower(evse.Connection.PartyID) +
		"/emsp/" + version + "/tariffs"
	uccReq := &domain.UccSyncRequest{
		CommandName: domain.UccObjectUpdateCommand,
		Params: domain.SyncUccParams{
			Method:     http.MethodPut,
			ObjectPath: objectPathURL,
			Module:     domain.Tariffs,
			Value:      string(value),
		},
		Targets: []string{},
	}
	return uccReq, nil
}

func (a *AppService) processSyncModule(ctx context.Context, message []byte, connection *domain.Connection, evse domain.EvseData, callback func(module string)) error {
	event := &domain.SyncModuleEvent{}
	if err := json.Unmarshal(message, event); err != nil {
		log.Err(err).Msg("unmarshal sync module error")
		return err
	}
	for _, item := range event.Data {
		var request *domain.UccSyncRequest
		switch item.Payload.OcpiObjectType {
		case domain.Locations:
			resLocSync, errLoc := a.locationSync(ctx, evse, connection)
			if errLoc != nil {
				log.Err(errLoc).Msg("error while fetching location")
				return nil
			}
			request = resLocSync
		case domain.Sessions:
			resSessionSync, errSession := a.sessionSync(ctx, evse, connection)
			if errSession != nil {
				log.Err(errSession).Msg("error while fetching session")
				return nil
			}
			request = resSessionSync
		case domain.Cdrs:
			resCdrSync, errCdr := a.cdrSync(ctx, evse, connection)
			if errCdr != nil {
				log.Err(errCdr).Msg("error while fetching cdr")
				return nil
			}
			request = resCdrSync
		case domain.Tariffs:
			resTariffSync, errTariff := a.tariffSync(ctx, evse, connection)
			if errTariff != nil {
				log.Err(errTariff).Msg("error while fetching tariff")
				return nil
			}
			request = resTariffSync
		}
		if request == nil {
			continue
		}
		request.Targets = append(request.Targets, item.DeviceID)
		errUcc := a.postToUCC(ctx, request)
		if errUcc != nil {
			log.Err(errUcc).Msg("post ucc error")
			return nil
		}
		callback(item.Payload.OcpiObjectType)
	}
	return nil
}

func NewAppService(cfg *config.Config, client *resty.Client, conSrv *ConnectionService, token hubauth.Token) *AppService {
	return &AppService{
		cfg:    cfg,
		conSrv: conSrv,
		client: client,
		token:  token,
	}
}
